import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import apiService from '../../services/apiService';
import EnhancedLoadingScreen from '../UI/EnhancedLoadingScreen';
import OceanBarChart from './OceanBarChart';
import AssessmentExplanations from './AssessmentExplanations';

const ResultOcean = () => {
  const { resultId } = useParams();
  const navigate = useNavigate();
  const [result, setResult] = useState(null);
  const [error, setError] = useState('');
  const fetchInProgressRef = useRef(false);
  const abortControllerRef = useRef(null);

  useEffect(() => {
    // Prevent duplicate calls
    if (fetchInProgressRef.current) {
      return;
    }

    const fetchResult = async (retryCount = 0) => {
      const maxRetries = 5;
      const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000); // Exponential backoff, max 10s

      // Create new AbortController for this fetch sequence
      abortControllerRef.current = new AbortController();

      try {
        fetchInProgressRef.current = true;
        const response = await apiService.getResultById(resultId);

        // Check if component is still mounted and request wasn't aborted
        if (!abortControllerRef.current?.signal.aborted) {
          if (response.success) {
            setResult(response.data);
            fetchInProgressRef.current = false;
          }
        }
      } catch (err) {
        // Check if the error is due to abort
        if (abortControllerRef.current?.signal.aborted) {
          return;
        }

        // If it's a 404 and we haven't exceeded max retries, try again
        if (err.response?.status === 404 && retryCount < maxRetries) {
          setTimeout(() => {
            // Check if component is still mounted before retrying
            if (!abortControllerRef.current?.signal.aborted) {
              fetchResult(retryCount + 1);
            }
          }, retryDelay);
        } else {
          // Final error after all retries or non-404 error
          const errorMessage = retryCount >= maxRetries
            ? `Result not found after ${maxRetries + 1} attempts. The analysis may still be processing.`
            : err.response?.data?.message || 'Failed to load results';
          setError(errorMessage);
          fetchInProgressRef.current = false;
        }
      }
    };

    if (resultId) {
      fetchResult();
    } else {
      navigate('/dashboard');
    }

    // Cleanup function
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      fetchInProgressRef.current = false;
    };
  }, [resultId, navigate]);

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getOceanInsights = (oceanData) => {
    if (!oceanData) return { high: [], low: [] };
    
    const entries = Object.entries(oceanData).sort(([,a], [,b]) => b - a);
    return {
      high: entries.filter(([,score]) => score >= 3.5),
      low: entries.filter(([,score]) => score < 2.5)
    };
  };

  const getTraitDescription = (trait) => {
    const descriptions = {
      'openness': 'Openness to experience reflects your willingness to try new things, think creatively, and embrace novel ideas and experiences.',
      'conscientiousness': 'Conscientiousness measures your tendency to be organized, responsible, dependable, and goal-oriented in your approach to tasks.',
      'extraversion': 'Extraversion indicates your preference for social interaction, assertiveness, and drawing energy from external stimulation.',
      'agreeableness': 'Agreeableness reflects your tendency to be cooperative, trusting, helpful, and considerate in your interactions with others.',
      'neuroticism': 'Neuroticism measures your emotional stability and tendency to experience negative emotions like anxiety, stress, or mood swings.'
    };
    return descriptions[trait] || 'A personality trait that influences behavior and preferences';
  };

  const getTraitImplications = (trait, score) => {
    const implications = {
      'openness': {
        high: ['Creative problem-solving', 'Adaptable to change', 'Enjoys learning new skills', 'Values artistic experiences'],
        low: ['Prefers routine and structure', 'Practical approach', 'Values tradition', 'Focused on proven methods']
      },
      'conscientiousness': {
        high: ['Strong work ethic', 'Reliable and punctual', 'Goal-oriented', 'Good at planning'],
        low: ['Flexible and spontaneous', 'Comfortable with ambiguity', 'Adaptable to change', 'Less rigid in approach']
      },
      'extraversion': {
        high: ['Energized by social interaction', 'Comfortable in leadership roles', 'Assertive communicator', 'Enjoys team environments'],
        low: ['Prefers quiet environments', 'Thoughtful and reflective', 'Works well independently', 'Careful decision-maker']
      },
      'agreeableness': {
        high: ['Cooperative team player', 'Empathetic and supportive', 'Avoids conflict', 'Values harmony'],
        low: ['Direct and honest communicator', 'Competitive nature', 'Independent thinker', 'Willing to challenge others']
      },
      'neuroticism': {
        high: ['Sensitive to stress', 'Emotionally responsive', 'Motivated by security', 'Benefits from support systems'],
        low: ['Emotionally stable', 'Calm under pressure', 'Resilient to stress', 'Consistent performance']
      }
    };
    
    return score >= 3.5 ? implications[trait]?.high || [] : implications[trait]?.low || [];
  };

  const getScoreLevel = (score) => {
    if (score >= 4.5) return { level: 'Very High', color: 'text-purple-600 bg-purple-100' };
    if (score >= 3.5) return { level: 'High', color: 'text-blue-600 bg-blue-100' };
    if (score >= 2.5) return { level: 'Moderate', color: 'text-yellow-600 bg-yellow-100' };
    if (score >= 1.5) return { level: 'Low', color: 'text-orange-600 bg-orange-100' };
    return { level: 'Very Low', color: 'text-red-600 bg-red-100' };
  };

  // Navigation cards data
  const navigationCards = [
    {
      title: 'Career Interests',
      subtitle: 'RIASEC Assessment',
      description: 'Explore your natural career interests and work preferences',
      icon: '🎯',
      path: `/results/${resultId}/riasec`,
      color: 'from-emerald-500 to-emerald-600'
    },
    {
      title: 'Character Strengths',
      subtitle: 'VIA-IS Assessment',
      description: 'Discover your core character strengths and values',
      icon: '⭐',
      path: `/results/${resultId}/via-is`,
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'Career Persona',
      subtitle: 'Integrated Profile',
      description: 'Your comprehensive career recommendations',
      icon: '🎪',
      path: `/results/${resultId}/persona`,
      color: 'from-indigo-500 to-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      {/* Main Content Area */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Loading State */}
        {!result && !error && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.3 }}
          >
            <EnhancedLoadingScreen
              title="Loading OCEAN Results..."
              subtitle="Fetching your personality analysis"
              skeletonCount={4}
              className="min-h-[600px]"
            />
          </motion.div>
        )}

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.4 }}
            className="bg-red-50 border border-red-200 rounded-xl p-6 shadow-sm"
          >
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Unable to Load Results</h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>{error}</p>
                </div>
                <div className="mt-4 space-x-3">
                  <button
                    onClick={() => window.location.reload()}
                    className="bg-red-100 text-red-800 px-3 py-2 rounded-lg text-sm font-medium hover:bg-red-200 transition-colors"
                  >
                    Retry
                  </button>
                  <button
                    onClick={() => navigate(`/results/${resultId}`)}
                    className="bg-gray-100 text-gray-800 px-3 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors"
                  >
                    Back to Overview
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        )}

        {/* Content State */}
        {result && (
          <>
            <div className="grid lg:grid-cols-12 gap-8">
            {/* Left Column - Main Content */}
            <div className="lg:col-span-8">
              {/* Header */}
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="mb-10"
              >
                <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
                  <div className="flex-1">
                    <motion.h1
                      className="text-4xl lg:text-5xl font-bold text-gray-900 mb-4"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.1 }}
                    >
                      Personality Traits
                      <span className="block text-2xl lg:text-3xl font-medium text-blue-600 mt-2">
                        OCEAN Assessment
                      </span>
                    </motion.h1>
                    <motion.p
                      className="text-lg text-gray-600 max-w-2xl leading-relaxed"
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.5, delay: 0.2 }}
                    >
                      Explore your unique personality profile through the Big Five model. Understanding these core traits
                      helps you recognize your natural tendencies, work preferences, and interpersonal style, providing
                      valuable insights for personal and professional development.
                    </motion.p>
                  </div>

                  <motion.div
                    className="flex flex-col sm:flex-row gap-3"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                  >
                    <button
                      onClick={() => navigate(`/results/${resultId}`)}
                      className="px-6 py-3 bg-white border border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 transition-all duration-200 font-medium shadow-sm"
                    >
                      ← Back to Overview
                    </button>
                    <button
                      onClick={() => navigate('/dashboard')}
                      className="px-6 py-3 bg-blue-600 text-white rounded-xl hover:bg-blue-700 transition-all duration-200 font-medium shadow-lg"
                    >
                      Dashboard
                    </button>
                  </motion.div>
                </div>

                <motion.div
                  className="mt-8 p-6 bg-white rounded-2xl shadow-sm border border-gray-100"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div className="flex items-center space-x-6 text-sm text-gray-600">
                      <div className="flex items-center">
                        <svg className="w-5 h-5 mr-2 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
                        </svg>
                        <span>Completed: {formatDate(result.created_at)}</span>
                      </div>
                      <div className="flex items-center">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mr-2"></div>
                        <span className="font-medium">Personality Analysis</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                        🧠 OCEAN Model
                      </span>
                    </div>
                  </div>
                </motion.div>
              </motion.div>

              {/* Introduction Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                className="mb-12"
              >
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 border border-blue-100">
                  <div className="max-w-4xl">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">
                      Understanding Your OCEAN Profile
                    </h2>
                    <div className="grid md:grid-cols-2 gap-6 text-gray-700">
                      <div>
                        <p className="mb-4 leading-relaxed">
                          The OCEAN model (also known as the Big Five) represents the most scientifically validated approach
                          to understanding personality. Each trait exists on a spectrum, and your scores reflect your natural
                          tendencies rather than fixed limitations.
                        </p>
                        <p className="leading-relaxed">
                          This assessment provides insights into how you typically think, feel, and behave across different
                          situations, helping you understand your work preferences and interpersonal style.
                        </p>
                      </div>
                      <div className="bg-white rounded-xl p-6 shadow-sm">
                        <h3 className="font-semibold text-gray-900 mb-3">The Five OCEAN Traits:</h3>
                        <div className="space-y-2 text-sm">
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                            <span><strong>Openness:</strong> Creativity & curiosity</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <span><strong>Conscientiousness:</strong> Organization & discipline</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                            <span><strong>Extraversion:</strong> Social energy & assertiveness</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                            <span><strong>Agreeableness:</strong> Cooperation & empathy</span>
                          </div>
                          <div className="flex items-center">
                            <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                            <span><strong>Neuroticism:</strong> Emotional sensitivity</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>

              {/* OCEAN Chart */}
              {result.assessment_data?.ocean && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="mb-12"
                >
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
                    <div className="text-center mb-8">
                      <h2 className="text-2xl font-bold text-gray-900 mb-2">
                        Your Personality Profile
                      </h2>
                      <p className="text-gray-600">
                        Visual representation of your Big Five personality traits
                      </p>
                    </div>
                    <OceanBarChart data={result.assessment_data.ocean} />
                  </div>
                </motion.div>
              )}

              {/* Personality Analysis */}
              {result.assessment_data?.ocean && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                  className="mb-12"
                >
                  <div className="text-center mb-10">
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">
                      Detailed Trait Analysis
                    </h2>
                    <p className="text-lg text-gray-600 max-w-3xl mx-auto">
                      Dive deeper into each personality dimension to understand how these traits influence
                      your behavior, preferences, and interactions with others.
                    </p>
                  </div>

                <div className="grid gap-8">
                  {/* All Traits Detailed View */}
                  <div className="bg-white rounded-2xl shadow-sm border border-gray-100 overflow-hidden">
                    <div className="bg-gradient-to-r from-gray-50 to-blue-50 p-6 border-b border-gray-100">
                      <h3 className="text-xl font-semibold text-gray-800 flex items-center">
                        <svg className="w-6 h-6 mr-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                        </svg>
                        Your Personality Breakdown
                      </h3>
                    </div>
                    <div className="p-6">
                      <div className="grid gap-8">
                        {Object.entries(result.assessment_data.ocean).map(([trait, score], index) => {
                          const scoreInfo = getScoreLevel(score);
                          const implications = getTraitImplications(trait, score);

                          return (
                            <motion.div
                              key={trait}
                              className="bg-gradient-to-r from-gray-50 to-blue-50/30 p-6 rounded-xl border border-gray-100"
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.5, delay: 0.8 + (index * 0.1) }}
                            >
                              <div className="flex justify-between items-center mb-4">
                                <h4 className="font-bold text-gray-900 capitalize text-xl">
                                  {trait}
                                </h4>
                                <div className="flex items-center space-x-3">
                                  <span className={`px-4 py-2 rounded-full text-sm font-semibold ${scoreInfo.color}`}>
                                    {scoreInfo.level}
                                  </span>
                                  <span className="text-gray-800 font-bold text-2xl">{score.toFixed(1)}</span>
                                </div>
                              </div>

                              <p className="text-gray-700 mb-6 leading-relaxed">{getTraitDescription(trait)}</p>

                              <div className="mb-6 bg-gray-200 rounded-full h-4 overflow-hidden">
                                <motion.div
                                  className="bg-gradient-to-r from-blue-500 to-indigo-600 h-4 rounded-full"
                                  initial={{ width: 0 }}
                                  animate={{ width: `${(score / 5) * 100}%` }}
                                  transition={{ duration: 1, delay: 1 + (index * 0.1) }}
                                ></motion.div>
                              </div>

                              {implications.length > 0 && (
                                <div>
                                  <h5 className="text-base font-semibold text-gray-800 mb-3">
                                    What this means for you:
                                  </h5>
                                  <div className="grid md:grid-cols-2 gap-3">
                                    {implications.map((implication, idx) => (
                                      <motion.div
                                        key={idx}
                                        className="text-sm text-gray-700 flex items-start bg-white p-3 rounded-lg border border-gray-100"
                                        initial={{ opacity: 0, y: 10 }}
                                        animate={{ opacity: 1, y: 0 }}
                                        transition={{ duration: 0.3, delay: 1.2 + (index * 0.1) + (idx * 0.05) }}
                                      >
                                        <span className="text-blue-500 mr-3 mt-0.5 font-bold">•</span>
                                        {implication}
                                      </motion.div>
                                    ))}
                                  </div>
                                </div>
                              )}
                            </motion.div>
                          );
                        })}
                      </div>
                    </div>
                  </div>

                  {/* Summary Insights */}
                  <div className="grid md:grid-cols-2 gap-8 mt-12">
                    {/* Strengths */}
                    {getOceanInsights(result.assessment_data.ocean).high.length > 0 && (
                      <motion.div
                        className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl shadow-sm border border-emerald-100 overflow-hidden"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 1.5 }}
                      >
                        <div className="bg-gradient-to-r from-emerald-500 to-green-600 p-6 text-white">
                          <h3 className="text-xl font-bold flex items-center">
                            <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                            Prominent Traits
                          </h3>
                          <p className="text-emerald-100 mt-2">Your strongest personality dimensions</p>
                        </div>
                        <div className="p-6">
                          <div className="space-y-4">
                            {getOceanInsights(result.assessment_data.ocean).high.map(([trait, score], index) => (
                              <motion.div
                                key={trait}
                                className="bg-white p-4 rounded-xl shadow-sm border border-emerald-100"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: 1.6 + (index * 0.1) }}
                              >
                                <div className="flex justify-between items-center">
                                  <span className="font-semibold text-gray-900 capitalize text-lg">{trait}</span>
                                  <span className="text-emerald-600 font-bold text-xl">{score.toFixed(1)}</span>
                                </div>
                                <div className="mt-2 bg-emerald-100 rounded-full h-2">
                                  <motion.div
                                    className="bg-emerald-500 h-2 rounded-full"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${(score / 5) * 100}%` }}
                                    transition={{ duration: 0.8, delay: 1.7 + (index * 0.1) }}
                                  ></motion.div>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}

                    {/* Areas for Balance */}
                    {getOceanInsights(result.assessment_data.ocean).low.length > 0 && (
                      <motion.div
                        className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl shadow-sm border border-blue-100 overflow-hidden"
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.5, delay: 1.5 }}
                      >
                        <div className="bg-gradient-to-r from-blue-500 to-indigo-600 p-6 text-white">
                          <h3 className="text-xl font-bold flex items-center">
                            <svg className="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                            </svg>
                            Areas for Balance
                          </h3>
                          <p className="text-blue-100 mt-2">Traits with room for development</p>
                        </div>
                        <div className="p-6">
                          <div className="space-y-4">
                            {getOceanInsights(result.assessment_data.ocean).low.map(([trait, score], index) => (
                              <motion.div
                                key={trait}
                                className="bg-white p-4 rounded-xl shadow-sm border border-blue-100"
                                initial={{ opacity: 0, y: 10 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.3, delay: 1.6 + (index * 0.1) }}
                              >
                                <div className="flex justify-between items-center">
                                  <span className="font-semibold text-gray-900 capitalize text-lg">{trait}</span>
                                  <span className="text-blue-600 font-bold text-xl">{score.toFixed(1)}</span>
                                </div>
                                <div className="mt-2 bg-blue-100 rounded-full h-2">
                                  <motion.div
                                    className="bg-blue-500 h-2 rounded-full"
                                    initial={{ width: 0 }}
                                    animate={{ width: `${(score / 5) * 100}%` }}
                                    transition={{ duration: 0.8, delay: 1.7 + (index * 0.1) }}
                                  ></motion.div>
                                </div>
                              </motion.div>
                            ))}
                          </div>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </motion.div>
            )}

              {/* Key Insights Section */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 2.0 }}
                className="mb-12"
              >
                <div className="bg-gradient-to-r from-gray-900 to-blue-900 rounded-2xl p-8 text-white">
                  <div className="max-w-4xl mx-auto text-center">
                    <h2 className="text-2xl font-bold mb-4">
                      Key Takeaways from Your OCEAN Profile
                    </h2>
                    <p className="text-gray-200 leading-relaxed mb-6">
                      Your personality profile is unique and valuable. Remember that these traits represent tendencies,
                      not limitations. Understanding your natural patterns can help you make informed decisions about
                      career paths, relationships, and personal development opportunities.
                    </p>
                    <div className="grid md:grid-cols-3 gap-6 mt-8">
                      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                        <div className="text-3xl mb-3">🎯</div>
                        <h3 className="font-semibold mb-2">Career Alignment</h3>
                        <p className="text-sm text-gray-200">
                          Use these insights to find roles that match your natural working style and preferences.
                        </p>
                      </div>
                      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                        <div className="text-3xl mb-3">🤝</div>
                        <h3 className="font-semibold mb-2">Team Dynamics</h3>
                        <p className="text-sm text-gray-200">
                          Understand how you interact with others and contribute to team environments.
                        </p>
                      </div>
                      <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6">
                        <div className="text-3xl mb-3">📈</div>
                        <h3 className="font-semibold mb-2">Personal Growth</h3>
                        <p className="text-sm text-gray-200">
                          Identify areas for development while leveraging your natural strengths.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Right Column - Navigation and Additional Content */}
            <div className="lg:col-span-4">
              {/* Navigation to Other Results */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 2.0 }}
                className="mb-12 sticky top-8"
              >
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">
                    Explore Your Complete Profile
                  </h2>
                  <p className="text-gray-600">
                    Continue your journey by exploring other aspects of your assessment results
                  </p>
                </div>

                <div className="space-y-4">
                  {navigationCards.map((card, index) => (
                    <motion.div
                      key={card.title}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: 2.1 + index * 0.1 }}
                      whileHover={{
                        y: -4,
                        transition: { duration: 0.2 }
                      }}
                      className="group cursor-pointer"
                      onClick={() => navigate(card.path)}
                    >
                      <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100 hover:shadow-lg hover:border-gray-200 transition-all duration-300">
                        <div className="flex items-start justify-between mb-4">
                          <div className={`w-12 h-12 bg-gradient-to-r ${card.color} rounded-xl flex items-center justify-center text-white text-xl group-hover:scale-110 transition-transform duration-200`}>
                            {/* Icon removed */}
                          </div>
                          <motion.svg
                            className="w-5 h-5 text-gray-400 group-hover:text-gray-600 transition-colors"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            whileHover={{ x: 3 }}
                            transition={{ duration: 0.2 }}
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                          </motion.svg>
                        </div>

                        <div>
                          <h3 className="text-lg font-bold text-gray-900 mb-1 group-hover:text-gray-700 transition-colors">
                            {card.title}
                          </h3>
                          <p className="text-sm text-gray-500 mb-3 font-medium">
                            {card.subtitle}
                          </p>
                          <p className="text-gray-600 text-sm leading-relaxed">
                            {card.description}
                          </p>
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            </div>
          </div>

            {/* Assessment Explanations */}
            <div className="lg:col-span-12 mt-12">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 2.4 }}
              >
                <AssessmentExplanations showOnly="ocean" />
              </motion.div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default ResultOcean;
